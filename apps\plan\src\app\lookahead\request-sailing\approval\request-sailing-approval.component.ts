import {
  Component,
  OnInit,
  inject,
  OnD<PERSON>roy,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
  DestroyRef,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { LookaheadSharedService } from 'libs/services/src/lib/services/shared/lookahead-subject.service';
import { lookaheadFeature } from '../../../../../../../libs/services/src/lib/services/lookahead/store/features';
import { map, startWith, Subscription } from 'rxjs';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import {
  NgxMatSelectSearchModule,
  MatSelectSearchComponent,
} from 'ngx-mat-select-search';
import {
  FormControl,
  ReactiveFormsModule,
  Validators,
  FormsModule,
  FormGroup,
} from '@angular/forms';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { As<PERSON><PERSON><PERSON><PERSON>, <PERSON>For, NgIf, NgClass, CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  timeAfter,
  timeBefore,
} from '../../../../../../../libs/components/src/lib/functions/utility.functions';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RepeatDialogComponent } from '../../repeat-dialog/repeat-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { Actions } from '@ngrx/effects';
import { PlanningDetails } from '../../../../../../../libs/services/src/lib/services/voyages/interfaces/planning-details.interface';
import { PlanningDetailsService } from '../../../shared/services/planning-details.service';
import { stripTimezoneOffset } from '../../../../../../../libs/services/src/lib/services/functions/convert-date.utils';
import { TimezoneService } from '../../../../../../../libs/services/src/lib/services/timezone.service';

@Component({
  selector: 'request-sailing-approval',
  templateUrl: './request-sailing-approval.component.html',
  styleUrls: ['./request-sailing-approval.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    MatSelectModule,
    NgxMatSelectSearchModule,
    FormsModule,
    ReactiveFormsModule,
    NgFor,
    CommonModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatIconModule,
    MatButtonModule,
  ],

  providers: [MatSelectSearchComponent],
})
export class RequestSailingApprovalComponent
  implements OnInit, OnChanges, OnDestroy
{
  private subscriptions = new Subscription();
  store = inject(Store);
  sharedService = inject(LookaheadSharedService);
  dialog = inject(MatDialog);
  vm$ = this.store.select(lookaheadFeature.selectLookaheadsState);
  @Input() originalVessels: Vessel[] = [];
  @Input() inboundSelection: boolean | null = false;
  @Input() outboundSelection: boolean | null = false;
  @Input() set showErrors(value: boolean) {
    if (value) {
      this.showValidationErrors = true;
    }
  }
  @Input() showApprovalErrors = false;
  @Output() approvalDataChanged = new EventEmitter<any>();
  vessels: Vessel[] = [];
  inboundVoyageSelected?: string | null;
  outboundVoyageSelected?: string | null;
  isInboundOutboundSidenavOpened = false;
  inboundVoyageActive = false;
  outboundVoyageActive = false;
  inboundVoyageHover = false;
  outboundVoyageHover = false;
  searchVesselControl = new FormControl('');
  timeAfter: Date | null = null;
  timeBefore: Date | null = null;
  minDifference = 1;
  today = new Date();
  editMode = false;
  selectedStatus = 0;
  showValidationErrors = false;
  form = new FormGroup({
    vesselId: new FormControl<string | null>(null, [Validators.required]),
    inboundVoyageId: new FormControl<string | null>(null),
    outboundVoyageId: new FormControl<string | null>(null),
    comment: new FormControl<string>(''),
    startTime: new FormControl<Date | null>(null, [Validators.required]),
    endTime: new FormControl<Date | null>(null, [Validators.required]),
    eta: new FormControl<string>(''),
    etd: new FormControl<string>(''),
    status: new FormControl<number>(0),
    doesRepeat: new FormControl<string>(''),
    seriesStartTime: new FormControl<Date | null>(null),
    seriesEndTime: new FormControl<Date | null>(null),
    weeklyPattern: new FormControl<string>(''),
    repeatEveryNumberOfWeeks: new FormControl<number | null>(null),
  });
  planningDetailsService = inject(PlanningDetailsService);
  timezoneService = inject(TimezoneService);

  private actions = inject(Actions);
  private destroyRef = inject(DestroyRef);
  ngOnInit() {
    this.updateValidators();

    this.loadPlanningDetails();

    this.subscriptions.add(
      this.sharedService.editPoolSailingRequestSubject.subscribe((data) => {
        this.editMode = true;
        if (data.SeriesStartTime !== null && data.SeriesEndTime !== null) {
          this.form.controls.doesRepeat.setValue('DoesRepeat');
          this.form.controls.seriesStartTime.setValue(data.SeriesStartTime);
          this.form.controls.seriesEndTime.setValue(data.SeriesEndTime);
        }
        this.patchForm(data);
      })
    );
    this.subscriptions.add(
      this.sharedService.closeInboundOutboundSidenavSubject.subscribe(() => {
        this.inboundVoyageActive = false;
        this.outboundVoyageActive = false;
      })
    );
    this.subscriptions.add(
      this.sharedService.updateRequestedInboundVoyageSubject.subscribe(
        (data) => {
          this.inboundVoyageSelected = data;
          this.form.get('inboundVoyageId')?.setValue(data);
          this.updateValidators();
        }
      )
    );
    this.subscriptions.add(
      this.sharedService.updateRequestedOutboundVoyageSubject.subscribe(
        (data) => {
          this.outboundVoyageSelected = data;
          this.form.get('outboundVoyageId')?.setValue(data);
          this.updateValidators();
        }
      )
    );
    this.subscriptions.add(
      this.sharedService.resetRequestSailingFormSubject.subscribe(() => {
        this.resetForm();
      })
    );
    this.subscriptions.add(
      this.form.valueChanges.subscribe((value) => {
        this.approvalDataChanged.emit(value);
      })
    );
  }

  loadPlanningDetails() {
    this.planningDetailsService
      .loadInboundPlanningDetails(this.destroyRef)
      .subscribe((planningDetails) => {
        if (planningDetails !== null) {
          const etaDate = new Date(planningDetails.eta!);
          const etaTimeString: string = etaDate.toTimeString().slice(0, 5);

          this.form.patchValue({
            startTime: etaDate,
            eta: etaTimeString,
          });
        }
      });

    this.planningDetailsService
      .loadOutboundPlanningDetails(this.destroyRef)
      .subscribe((planningDetails) => {
        if (planningDetails !== null) {
          const etdDate = new Date(planningDetails.etd!);
          const etdTimeString: string = etdDate.toTimeString().slice(0, 5);

          this.form.patchValue({
            endTime: etdDate,
            etd: etdTimeString,
          });
        }
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['vessels']) {
      this.vessels = Object.assign(this.originalVessels);
    }

    if (changes['inboundSelection'] || changes['outboundSelection']) {
      if (changes['inboundSelection'] && !this.inboundSelection) {
        this.form.get('inboundVoyageId')?.setValue(null);
      }
      if (changes['outboundSelection'] && !this.outboundSelection) {
        this.form.get('outboundVoyageId')?.setValue(null);
      }
      this.updateValidators();
    }

    this.filters();

    if (changes['showApprovalErrors']) {
      this.showValidationErrors = this.showApprovalErrors;
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private patchForm(sailingRequest: any): void {
    // Check and adjust ETA/ETD for DST if needed
    let adjustedEta = sailingRequest.Eta;
    let adjustedEtd = sailingRequest.Etd;

    if (
      sailingRequest.StartTime &&
      sailingRequest.EndTime &&
      sailingRequest.Eta &&
      sailingRequest.Etd
    ) {
      const startTime = new Date(sailingRequest.StartTime);
      const endTime = new Date(sailingRequest.EndTime);

      // Check if times need DST adjustment
      const dstAdjustment = this.timezoneService.checkAndAdjustTimesForDST(
        startTime,
        endTime,
        sailingRequest.Eta,
        sailingRequest.Etd,
        sailingRequest.OriginalTimezone // This would need to be stored when creating the request
      );

      if (dstAdjustment.dstAdjustmentMade) {
        adjustedEta = dstAdjustment.adjustedEta;
        adjustedEtd = dstAdjustment.adjustedEtd;

        // Log the adjustment for debugging
        console.log('DST adjustment made in approval component:', {
          originalEta: sailingRequest.Eta,
          adjustedEta,
          originalEtd: sailingRequest.Etd,
          adjustedEtd,
        });
      }
    }

    this.form.patchValue({
      vesselId: sailingRequest.VesselId,
      startTime: sailingRequest.StartTime,
      endTime: sailingRequest.EndTime,
      inboundVoyageId: sailingRequest.InboundVoyageId,
      outboundVoyageId: sailingRequest.OutboundVoyageId,
      weeklyPattern: sailingRequest.WeeklyPattern,
      eta: adjustedEta,
      etd: adjustedEtd,
      comment: sailingRequest.Comment,
      status: sailingRequest.Status,
    });
    this.selectedStatus = sailingRequest.Status;
    this.inboundVoyageSelected = sailingRequest.InboundVoyageId;
    this.outboundVoyageSelected = sailingRequest.OutboundVoyageId;
    this.updateValidators();
  }

  private filters(): void {
    this.searchVesselControl.valueChanges
      .pipe(
        startWith(''),
        map((value) => this._filterVessels(value!))
      )
      .subscribe((filteredVessels) => {
        this.vessels = filteredVessels;
      });
  }

  private _filterVessels(value: string): Vessel[] {
    if (value === '') {
      return this.originalVessels;
    }

    const filterValue = value.toLowerCase();
    const numericFilterValue = value.toString().toLowerCase();

    return this.originalVessels.filter(
      (vessel) =>
        vessel.name.toLowerCase().includes(filterValue) ||
        vessel.name.toLowerCase().includes(numericFilterValue)
    );
  }

  vesselNotSelectedOrSeriesSelected(): boolean {
    return (
      !this.form.get('vesselId')?.value ||
      (!this.editMode &&
        this.form.value.seriesStartTime !== null &&
        this.form.value.seriesEndTime !== null)
    );
  }

  openInboundOutboundSidebar(voyageType: string): void {
    this.isInboundOutboundSidenavOpened = true;
    if (voyageType === 'Inbound') {
      this.inboundVoyageActive = true;
      this.outboundVoyageActive = false;
    } else {
      this.inboundVoyageActive = false;
      this.outboundVoyageActive = true;
    }
    this.sharedService.openRequestedInboundOutboundVoyage(
      true,
      voyageType,
      this.form.get('vesselId')?.value!
    );
  }

  changeStartDate(value: Date | null | undefined): void {
    this.timeBefore = timeBefore(value!, { min: this.minDifference });
    this.form.controls.endTime.setValidators([
      Validators.required,
      Validators.min(value!.getTime()),
    ]);
    this.form.controls.endTime.updateValueAndValidity();
  }

  setDoesRepeat(value: string) {
    this.form.get('doesRepeat')?.setValue(value);
    if (value === 'DoesRepeat') {
      this.openRepeatDialog();
    } else {
      this.form.get('seriesStartTime')?.setValue(null);
      this.form.get('seriesEndTime')?.setValue(null);
      this.form.get('weeklyPattern')?.setValue('');
      this.form.get('repeatEveryNumberOfWeeks')?.setValue(null);
    }
  }

  openRepeatDialog() {
    const dialogRef = this.dialog.open(RepeatDialogComponent, {
      width: '600px',
      disableClose: true,
      data: {
        startDate:
          this.form.value.seriesStartTime !== null
            ? this.form.value.seriesStartTime
            : this.timeBefore
            ? new Date(this.timeBefore)
            : null,
        endDate:
          this.form.value.seriesEndTime !== null
            ? this.form.value.seriesEndTime
            : this.timeAfter
            ? new Date(this.timeAfter)
            : new Date(new Date().getFullYear(), 11, 31),
        weeklyPattern: this.form.value.weeklyPattern,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (!this.editMode) {
          this.form.get('startTime')?.setValue(result.startDate);
          this.form.get('endTime')?.setValue(result.endDate);
        }
        this.form.get('seriesStartTime')?.setValue(result.startDate);
        this.form.get('seriesEndTime')?.setValue(result.endDate);
        this.form.get('weeklyPattern')?.setValue(result.weeklyPattern);
        this.form
          .get('repeatEveryNumberOfWeeks')
          ?.setValue(result.repeatEveryNumberOfWeeks);
      } else {
        if (
          this.form.value.seriesStartTime === null &&
          this.form.value.seriesEndTime === null
        ) {
          this.form.get('doesRepeat')?.setValue('');
        }
      }
    });
  }

  changeEndDate(value: Date): void {
    this.timeAfter = timeAfter(value, { min: this.minDifference - 1 });
    this.form.controls.startTime.setValidators([
      Validators.required,
      Validators.max(value.getTime()),
    ]);
    this.form.controls.startTime.updateValueAndValidity();
  }

  getMinStartDate(): Date | null | undefined {
    return this.editMode ? this.form.get('startTime')?.value : new Date();
  }

  selectStatus(status: number): void {
    this.selectedStatus = status;
    this.form.get('status')?.setValue(status);
    this.showValidationErrors = true;
  }

  updateInboundOutbound(): void {
    this.sharedService.updateRequestedInboundVoyage(null);
    this.sharedService.updateRequestedOutboundVoyage(null);
    this.updateValidators();
  }

  resetForm(): void {
    this.form.reset();
    this.timeAfter = null;
    this.timeBefore = new Date();
    this.selectedStatus = 0;
    this.editMode = false;
    this.inboundVoyageSelected = null;
    this.outboundVoyageSelected = null;
    this.updateValidators();
  }

  inboundOutboundVoyageSelected() {
    return (
      (!this.editMode && this.form.value.inboundVoyageId !== null) ||
      this.form.value.outboundVoyageId !== null
    );
  }

  cannotAssignVoyage(): boolean {
    return (
      !this.editMode &&
      this.form.value.seriesStartTime !== null &&
      this.form.value.seriesEndTime !== null
    );
  }

  isEtaRequired(): boolean {
    return this.form.get('inboundVoyageId')?.value !== null;
  }

  isEtdRequired(): boolean {
    return this.form.get('outboundVoyageId')?.value !== null;
  }

  updateValidators(): void {
    const etaCtrl = this.form.get('eta');
    const etdCtrl = this.form.get('etd');

    if (this.isEtaRequired()) {
      etaCtrl?.setValidators([Validators.required]);
    } else {
      etaCtrl?.clearValidators();
    }

    if (this.isEtdRequired()) {
      etdCtrl?.setValidators([Validators.required]);
    } else {
      etdCtrl?.clearValidators();
    }

    etaCtrl?.updateValueAndValidity();
    etdCtrl?.updateValueAndValidity();
  }
}
