import { Component, computed, inject, Input, OnInit } from '@angular/core';
import { NgI<PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { TransportRequestActions } from 'libs/services/src/lib/services/transport-requests/store/actions/transport-requests.actions';
import { transportRequestFeature } from 'libs/services/src/lib/services/transport-requests/store/features/transport-request.feature';
import { Store } from '@ngrx/store';
import { lookaheadFeature } from 'libs/services/src/lib/services/lookahead/store/features';
import { AccordionModule } from 'primeng/accordion';
import { TransportRequestHeaderComponent } from './transport-request-header/transport-request-header.component';
import { ChipModule } from 'primeng/chip';
import { CargoListComponent } from './cargo-list/cargo-list.component';
import { ActivatedRoute, Router } from '@angular/router';
import { TransportRequestAttachmentComponent } from './transport-request-attachment/transport-request-attachment.component';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { Actions } from '@ngrx/effects';
import { TransportRequestProgressStatusComponent } from './progress-status/transport-request-progress-status.component';
import { TransportRequestCargoListStatusComponent } from './cargo-list-status/transport-request-cargo-list-status.component';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { TransportRequest } from 'libs/services/src/lib/services/transport-requests/interfaces/transport-request.interface';

import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'transport-request-page',
  templateUrl: './transport-requests.component.html',
  styleUrls: ['./transport-requests.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    NgFor,
    AccordionModule,
    TransportRequestHeaderComponent,
    ChipModule,
    ButtonModule,
    CargoListComponent,
    TransportRequestAttachmentComponent,
    TransportRequestProgressStatusComponent,
    BreadcrumbModule,
    TransportRequestCargoListStatusComponent,
    CardModule,
  ],
})
export class TransportRequestsComponent implements OnInit {
  @Input() sailingRequestId = '';
  @Input() transportRequestId = '';
  private readonly confirmationService = inject(ConfirmationService);

  actions = inject(Actions);
  store = inject(Store);
  router = inject(Router);
  route = inject(ActivatedRoute);

  isOpenConfirmDialog: boolean = false;
  allApproved: boolean = true;
  request: TransportRequest | null = null;
  numberOfUnapprovedCargos: number = 0;

  sailingRequest = this.store.selectSignal(
    lookaheadFeature.selectSailingRequest
  );
  transportRequest = this.store.selectSignal(
    transportRequestFeature.selectTransportRequest
  );
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  severity: string = 'success';

  breadcrumb = computed<MenuItem[]>(() => {
    const fragment = this.route.snapshot.fragment ?? undefined;
    const clusterName = this.sailingRequest()?.clusterName;
    const voyageDirectionName = this.transportRequest()?.voyageDirectionName;

    const items: MenuItem[] = [
      {
        label: 'Lookahead',
        routerLink: '/sailing-schedule',
        styleClass: 'active',
      },
      {
        label: 'Active Transport Requests',
        routerLink: `/order-processing/active-requests/${
          this.transportRequest()?.voyageId ? 'assigned' : 'unassigned'
        }/all`,
        fragment: fragment,
        styleClass: 'active',
      },
      {
        label: clusterName
          ? `${clusterName} - ${voyageDirectionName ?? ''}`
          : voyageDirectionName ?? '',
      },
    ];

    return items;
  });

  ngOnInit() {
    this.store.dispatch(
      TransportRequestActions.load_Transport_Requests_Page({
        transportRequestId: this.transportRequestId,
        sailingRequestId: this.sailingRequestId,
      })
    );
  }

  accordionChanged(isOpen: boolean, childComponent: any) {
    childComponent.setAccordionState(isOpen);
  }

  submitTR(event: Event) {
    if (!this.allApproved) {
      this.onSubmit(event);
    } else {
      this.updateTransportRequest(true);
    }
  }

  updateTransportRequest(isComplete: boolean) {
    const model = {
      ...this.transportRequest()!,
      isComplete: isComplete,
    };

    this.store.dispatch(
      TransportRequestActions.edit_Transport_Request({
        transportRequestId: this.transportRequestId,
        transportRequest: model,
      })
    );
  }

  onSubmit(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message:
        'There are ' +
        this.numberOfUnapprovedCargos +
        ' outstanding cargo lines still awaiting approval.',
      header: 'Complete Transport Request',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Submit',
      rejectLabel: 'Cancel',
      accept: () => {
        this.updateTransportRequest(true);
      },
    });
  }

  onReopen(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to Reopen this Transport Request?',
      header: 'Reopen Transport Request',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Reopen',
      rejectLabel: 'Cancel',
      accept: () => {
        this.updateTransportRequest(false);
      },
    });
  }
}
