# DST and Timezone Handling Solution

## Problem Description

The lookahead request-sailing components had an issue where ETA and ETD times were not properly adjusted when users changed timezones, particularly when Daylight Saving Time (DST) transitions were involved.

**Scenario:**
1. User in UTC+3 timezone selects ETA: 10:00, ETD: 13:00
2. Backend converts and stores these as UTC: 07:00, 10:00
3. User changes timezone to UTC+0
4. Times display as raw UTC values without proper DST consideration
5. If DST is active, times may be off by 1 hour

## Solution Overview

Created a comprehensive timezone and DST handling system with the following components:

### 1. Enhanced Date Conversion Utilities (`libs/services/src/lib/services/functions/convert-date.utils.ts`)

**New Functions Added:**
- `isDaylightSavingTime(date: Date, timezone: string): boolean` - Detects if a date is in DST
- `convertUtcToUserTimezone(utcDate: Date, userTimezone: string): Date` - DST-aware UTC to local conversion
- `convertUserTimezoneToUtc(localDate: Date, userTimezone: string): Date` - DST-aware local to UTC conversion
- `adjustTimeForTimezoneChange()` - Adjusts ETA/ETD when timezone context changes

### 2. Timezone Service (`libs/services/src/lib/services/timezone.service.ts`)

**Key Features:**
- Centralized timezone management
- DST detection and adjustment
- User timezone context awareness
- Debug utilities for troubleshooting

**Main Methods:**
- `checkAndAdjustTimesForDST()` - Core function to check and adjust ETA/ETD times
- `getCurrentUserTimezone()` - Gets user's current timezone
- `isDateInDST()` - Checks if a date is in DST period
- `getTimezoneInfo()` - Debug information

### 3. Component Integration

**Updated Components:**
- `request-sailing.component.ts` - Main sailing request component
- `request-sailing-approval.component.ts` - Approval sub-component

**Integration Points:**
- `patchForm()` methods now check for DST adjustments when loading saved requests
- Manual DST check buttons added for testing/debugging
- Automatic DST detection when timezone context changes

## Usage

### Automatic DST Adjustment

When loading a saved sailing request, the system automatically:
1. Checks if the start/end times fall within DST periods
2. Compares original timezone context with current user timezone
3. Adjusts ETA/ETD times if DST transition affects them
4. Logs adjustments for debugging

### Manual DST Check

Users can manually trigger DST checks using the debug buttons:
- **"Check DST"** - Manually adjusts current ETA/ETD for DST
- **"Debug TZ"** - Shows timezone information in console

### Developer Usage

```typescript
// Inject the service
timezoneService = inject(TimezoneService);

// Check and adjust times
const adjustment = this.timezoneService.checkAndAdjustTimesForDST(
  startTime,
  endTime,
  eta,
  etd,
  originalTimezone
);

if (adjustment.dstAdjustmentMade) {
  // Apply adjusted times
  this.form.patchValue({
    eta: adjustment.adjustedEta,
    etd: adjustment.adjustedEtd
  });
}
```

## Technical Implementation

### DST Detection Algorithm

1. **Reference Points**: Creates January (non-DST) and July (likely DST) dates
2. **Offset Comparison**: Compares timezone offsets between reference dates
3. **Target Date Check**: Determines if target date has DST offset
4. **Transition Detection**: Identifies dates near DST transitions

### Timezone Conversion

Uses `Intl.DateTimeFormat` for accurate timezone conversions:
- Handles DST transitions automatically
- Supports all IANA timezone identifiers
- Maintains date/time precision

### Error Handling

- Graceful fallbacks for invalid timezones
- Console warnings for conversion errors
- Preserves original values if conversion fails

## Testing

### Test Scenarios

1. **DST Transition Dates**: Test around spring/fall DST changes
2. **Timezone Changes**: Switch user timezone and verify adjustments
3. **Non-DST Timezones**: Verify no adjustments for non-DST zones
4. **Edge Cases**: Test with invalid dates/timezones

### Debug Tools

- Console logging for all adjustments
- Timezone information display
- Manual adjustment triggers
- Debug buttons in UI (development only)

## Configuration

### User Timezone Storage

User timezone is stored in `user.locationTimeZoneInfoId` and accessed via:
```typescript
const timezone = this.currentUser()?.locationTimeZoneInfoId ?? 'UTC';
```

### Timezone Format

Uses IANA timezone identifiers (e.g., 'Europe/London', 'America/New_York')

## Future Enhancements

1. **Original Timezone Storage**: Store timezone when request is created
2. **User Notifications**: Alert users when DST adjustments are made
3. **Bulk Adjustment**: Adjust multiple requests when timezone changes
4. **Historical DST**: Handle historical DST rule changes
5. **UI Indicators**: Visual indicators for DST-adjusted times

## Troubleshooting

### Common Issues

1. **Times not adjusting**: Check if original timezone is available
2. **Incorrect adjustments**: Verify timezone identifiers are valid
3. **Console errors**: Check browser timezone support

### Debug Steps

1. Use "Debug TZ" button to check timezone info
2. Check console for adjustment logs
3. Verify user timezone in user object
4. Test with known DST transition dates

## Dependencies

- **Intl.DateTimeFormat**: Native browser API for timezone handling
- **Angular Signals**: For reactive user timezone access
- **NgRx Store**: For user state management

No additional external libraries required.
