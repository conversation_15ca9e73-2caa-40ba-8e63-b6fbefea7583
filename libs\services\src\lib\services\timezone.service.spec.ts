import { TestBed } from '@angular/core/testing';
import { Store } from '@ngrx/store';
import { TimezoneService } from './timezone.service';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';

describe('TimezoneService', () => {
  let service: TimezoneService;
  let mockStore: jasmine.SpyObj<Store>;

  beforeEach(() => {
    const storeSpy = jasmine.createSpyObj('Store', ['selectSignal']);
    
    TestBed.configureTestingModule({
      providers: [
        TimezoneService,
        { provide: Store, useValue: storeSpy }
      ]
    });
    
    service = TestBed.inject(TimezoneService);
    mockStore = TestBed.inject(Store) as jasmine.SpyObj<Store>;
    
    // Mock the selectSignal to return a signal with a mock user
    mockStore.selectSignal.and.returnValue(() => ({
      locationTimeZoneInfoId: 'Europe/London'
    }));
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get current user timezone', () => {
    const timezone = service.getCurrentUserTimezone();
    expect(timezone).toBe('Europe/London');
  });

  it('should detect DST for London timezone', () => {
    // July date (should be DST in London)
    const julyDate = new Date(2024, 6, 15); // July 15, 2024
    const isDST = service.isDateInDST(julyDate);
    
    // January date (should not be DST in London)
    const januaryDate = new Date(2024, 0, 15); // January 15, 2024
    const isNotDST = service.isDateInDST(januaryDate);
    
    expect(isDST).toBe(true);
    expect(isNotDST).toBe(false);
  });

  it('should check and adjust times for DST', () => {
    const startTime = new Date(2024, 6, 15, 10, 0); // July 15, 2024 10:00
    const endTime = new Date(2024, 6, 15, 13, 0); // July 15, 2024 13:00
    const eta = '10:00';
    const etd = '13:00';
    const originalTimezone = 'UTC';

    const result = service.checkAndAdjustTimesForDST(
      startTime,
      endTime,
      eta,
      etd,
      originalTimezone
    );

    expect(result).toBeDefined();
    expect(result.adjustedEta).toBeDefined();
    expect(result.adjustedEtd).toBeDefined();
    expect(typeof result.dstAdjustmentMade).toBe('boolean');
  });

  it('should format date in user timezone', () => {
    const testDate = new Date('2024-07-15T10:00:00Z');
    const formatted = service.formatDateInUserTimezone(testDate, 'time');
    
    expect(formatted).toMatch(/^\d{2}:\d{2}$/); // Should match HH:mm format
  });

  it('should get timezone info', () => {
    const testDate = new Date(2024, 6, 15);
    const info = service.getTimezoneInfo(testDate);
    
    expect(info).toBeDefined();
    expect(info.timezone).toBe('Europe/London');
    expect(typeof info.isInDST).toBe('boolean');
    expect(typeof info.offset).toBe('number');
    expect(typeof info.isDSTTransition).toBe('boolean');
  });

  it('should handle UTC timezone fallback', () => {
    // Mock user with no timezone
    mockStore.selectSignal.and.returnValue(() => ({
      locationTimeZoneInfoId: null
    }));
    
    const timezone = service.getCurrentUserTimezone();
    expect(timezone).toBe('UTC');
  });
});
