<div *ngIf="vm$ | async as vm">
  <div class="sidebar-header">
    <h2>{{ editMode ? 'Edit' : 'Add' }} Request</h2>
    <button mat-icon-button (click)="toggleRequest()">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <div class="sidebar-content">
    <p>Complete the below section for your request sailing.</p>
    <mat-tab-group
      class="tab-group"
      [(selectedIndex)]="selectedTabIndex"
      (selectedTabChange)="onTabChange($event)"
    >
      <mat-tab label="Vessel info" class="custom-tab-label">
        <div class="form-section">
          <form [formGroup]="form">
            <div class="form-fields">
              <div class="row pt-10" style="align-content: center">
                <img
                  src="assets/arrow.svg"
                  alt="users-plus"
                  style="margin-right: 4px; height: 20px; width: 20px"
                />
                <h3>{{ headerText }}</h3>
              </div>
              <p style="margin-bottom: 16px">
                Select cluster to create a request.
              </p>

              <ng-container *ngIf="isPartOfCluster; else notPartOfCluster">
                <mat-form-field
                  class="field"
                  appearance="outline"
                  hideRequiredMarker="true"
                >
                  <mat-label>
                    <img
                      src="assets/cluster.svg"
                      alt="add-cluster"
                      style="margin-right: 8px"
                    />
                    Add cluster
                  </mat-label>
                  <mat-select
                    formControlName="clusterId"
                    (selectionChange)="onClusterChange($event.value)"
                  >
                    <mat-option>
                      <ngx-mat-select-search
                        placeholderLabel="Search..."
                        [formControl]="searchClusterControl"
                        noEntriesFoundLabel="No Results found..."
                      ></ngx-mat-select-search>
                    </mat-option>
                    <mat-option
                      *ngFor="let cluster of clusterAssets"
                      [value]="cluster.assetId"
                      class="dropdown-hover"
                    >
                      {{ cluster.name }}
                    </mat-option>
                  </mat-select>
                  <button
                    mat-icon-button
                    matSuffix
                    *ngIf="form.get('clusterId')?.value"
                    (click)="clearClusterSelection()"
                  >
                    <mat-icon>clear</mat-icon>
                  </button>
                </mat-form-field>
              </ng-container>
              <ng-template #notPartOfCluster>
                <mat-form-field
                  class="field"
                  appearance="outline"
                  hideRequiredMarker="true"
                >
                  <mat-label>
                    <img
                      src="assets/cluster.svg"
                      alt="add-cluster"
                      style="margin-right: 8px"
                    />
                    Cluster Status
                  </mat-label>
                  <input
                    matInput
                    readonly
                    value="Not part of a cluster"
                    class="disabled"
                  />
                </mat-form-field>
              </ng-template>

              <div class="container">
                <div class="row" style="align-content: center">
                  <h4>
                    Offshore Installations <span style="color: red">*</span>
                  </h4>
                </div>
                <p style="margin-bottom: 16px">
                  Only select the offshore installations to be visited
                </p>

                <mat-form-field class="field" appearance="outline">
                  <mat-label>
                    <span style="vertical-align: top"
                      >Offshore installations</span
                    >
                  </mat-label>
                  <mat-select
                    multiple
                    formControlName="sailingRequestAssets"
                    (selectionChange)="onInstallationSelectionChange()"
                  >
                    <mat-option>
                      <ngx-mat-select-search
                        [formControl]="searchInstallationControl"
                        placeholderLabel="Search..."
                        noEntriesFoundLabel="No Results found..."
                      ></ngx-mat-select-search>
                    </mat-option>
                    <mat-option
                      *ngFor="let installation of filteredOffshoreInstallations"
                      [value]="installation.assetId"
                      class="dropdown-hover"
                    >
                      {{ installation.name }}
                    </mat-option>
                  </mat-select>
                  <button
                    mat-icon-button
                    matSuffix
                    *ngIf="form.get('sailingRequestAssets')?.value?.length"
                    (click)="clearInstallationSelection()"
                  >
                    <mat-icon>clear</mat-icon>
                  </button>
                  <mat-error
                    class="error"
                    *ngIf="
                      nextButtonClicked &&
                      !form.get('sailingRequestAssets')?.value?.length
                    "
                  >
                    Offshore installations are required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="container">
                <div class="row" style="align-content: center">
                  <h4>Types <span style="color: red">*</span></h4>
                </div>
                <p style="margin-bottom: 16px">
                  Select a direction. Transport request requires a selection
                </p>
                <div class="row select-all">
                  <mat-label>
                    <input
                      type="checkbox"
                      formControlName="selectAll"
                      (click)="selectAll($event)"
                    />
                    Select all
                  </mat-label>
                </div>
                <div class="row">
                  <div class="col image">
                    <button
                      class="toggle-button"
                      style="margin-left: -8px"
                      (click)="inboundSelect()"
                      (mouseenter)="inboundHover = true"
                      (mouseleave)="inboundHover = false"
                    >
                      <img
                        *ngIf="!form.controls.isInbound.value && !inboundHover"
                        src="assets/inbound.svg"
                      />
                      <img
                        class="select-image"
                        *ngIf="form.controls.isInbound.value"
                        src="assets/inbound-selected-active.svg"
                      />
                      <img
                        class="select-image"
                        *ngIf="!form.controls.isInbound.value && inboundHover"
                        src="assets/inbound-hover.svg"
                      />
                    </button>
                  </div>
                  <div class="col image">
                    <button
                      class="toggle-button"
                      (click)="outboundSelect()"
                      (mouseenter)="outboundHover = true"
                      (mouseleave)="outboundHover = false"
                    >
                      <img
                        *ngIf="
                          !form.controls.isOutbound.value && !outboundHover
                        "
                        src="assets/outbound.svg"
                      />
                      <img
                        class="select-image"
                        *ngIf="form.controls.isOutbound.value"
                        src="assets/outbound-selected-active.svg"
                      />
                      <img
                        class="select-image"
                        *ngIf="!form.controls.isOutbound.value && outboundHover"
                        src="assets/outbound-hover.svg"
                      />
                    </button>
                  </div>
                  <div class="col image">
                    <button
                      class="toggle-button"
                      (click)="interfieldSelect()"
                      (mouseenter)="interfieldHover = true"
                      (mouseleave)="interfieldHover = false"
                    >
                      <img
                        *ngIf="
                          !form.controls.isInterfield.value && !interfieldHover
                        "
                        src="assets/interfield.svg"
                      />
                      <img
                        *ngIf="form.controls.isInterfield.value"
                        src="assets/interfield-selected.svg"
                      />
                      <img
                        *ngIf="
                          !form.controls.isInterfield.value && interfieldHover
                        "
                        src="assets/interfield-hover.svg"
                      />
                    </button>
                  </div>
                </div>
                <mat-error class="error" *ngIf="outboundNotSelected()"
                  >Interfield request detected, please add an outbound
                  voyage</mat-error
                >
                <mat-error
                  class="error"
                  *ngIf="nextButtonClicked && voyageTypeNotSelected()"
                  >Type of sailing is required</mat-error
                >
              </div>
            </div>
          </form>
        </div>
      </mat-tab>
      <mat-tab [disabled]="isRequirementDisabled()" label="Requirement">
        <div class="form-section pt-10">
          <request-sailing-requirement
            [activityCategories]="filteredActivities"
            [units]="units"
            [showErrors]="showErrors"
            [currentUser]="currentUser"
            [originalClients]="clients"
            (sendDataToParent)="handleDataFromChild($event)"
          ></request-sailing-requirement>
        </div>
      </mat-tab>
      <mat-tab
        *ngIf="!hasOnlyCliRoleInPlan()"
        [disabled]="isApprovalsDisabled()"
        label="Approvals"
      >
        <div class="form-section">
          <request-sailing-approval
            [originalVessels]="vessels"
            [inboundSelection]="form.controls.isInbound.value"
            [outboundSelection]="form.controls.isOutbound.value"
            [showErrors]="showApprovalErrors"
            (approvalDataChanged)="approvalDataChanged($event)"
          ></request-sailing-approval>
        </div>
      </mat-tab>
      <mat-tab *ngIf="editMode">
        <ng-template mat-tab-label>
          Comments
          <span *ngIf="addUserToComments" class="red-circle"></span>
        </ng-template>
        <request-sailing-comments
          [currentUser]="currentUser"
          [requestSailingId]="requestSailingId"
          [comments]="sailingRequestUserComments"
        >
        </request-sailing-comments>
      </mat-tab>
    </mat-tab-group>

    <div class="row">
      <div class="button-row">
        <button mat-button (click)="toggleRequest()" type="button">
          Cancel
        </button>

        <div
          *ngIf="
            selectedTabIndex === 0 ||
            (selectedTabIndex === 1 && !hasOnlyCliRoleInPlan())
          "
        >
          <button
            mat-raised-button
            color="warn"
            (click)="goToNextTab()"
            [disabled]="
              outboundNotSelected() ||
              !form.get('sailingRequestAssets')?.value?.length ||
              voyageTypeNotSelected()
            "
            type="button"
          >
            Next
          </button>
        </div>
        <div
          *ngIf="
            (selectedTabIndex === 1 && hasOnlyCliRoleInPlan()) ||
            selectedTabIndex === 2
          "
        >
          <!-- DST Debug Buttons (for development/testing) -->
          <button
            mat-button
            color="accent"
            (click)="checkAndAdjustForDST()"
            type="button"
            style="margin-right: 8px"
            title="Check and adjust ETA/ETD for DST changes"
          >
            Check DST
          </button>
          <button
            mat-button
            color="accent"
            (click)="getTimezoneDebugInfo()"
            type="button"
            style="margin-right: 8px"
            title="Show timezone debug information in console"
          >
            Debug TZ
          </button>

          <button
            [disabled]="vm.loading.createEdit"
            class="save"
            (click)="save()"
            mat-raised-button
            color="warn"
            type="submit"
          >
            <span [ngStyle]="{ opacity: vm.loading.createEdit ? '0' : '1' }">
              Save
            </span>
            <mat-icon class="mat-icon--spinner" *ngIf="vm.loading.createEdit"
              ><mat-spinner diameter="25"></mat-spinner
            ></mat-icon>
          </button>
        </div>
        <div
          *ngIf="
            editMode && selectedTabIndex === 4 && this.form.value.status === 3
          "
        >
          <button
            class="save"
            (click)="deleteSailingRequest()"
            mat-raised-button
            color="warn"
            type="submit"
          >
            Confirm Delete
          </button>
        </div>
        <div *ngIf="selectedTabIndex === 4">
          <button
            class="save"
            (click)="allowChildToSendComment()"
            mat-raised-button
            color="warn"
            type="submit"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
