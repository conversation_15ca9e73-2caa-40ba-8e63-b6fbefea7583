import { SailingRequestStatus } from '../enums/sailing-request-status.enum';
import { SailingRequestActivity } from './sailing-request-activity.interface';
import { SailingRequestAsset } from './sailing-request-asset.interface';
import { SailingRequestType } from './sailing-request-type.enum';
import { SailingRequestUserComment } from './sailing-request-user-comment.interface';
import { Voyage } from '../../voyages/interfaces/voyage.interface';
import { TransportRequest } from '../../transport-requests/interfaces/transport-request.interface';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';

export interface SailingRequest {
  sailingRequestId: string;
  startTime: Date | null;
  seriesStartTime: Date | null;
  endTime: Date | null;
  seriesEndTime: Date | null;
  eta: string | null;
  etd: string | null;
  pilot: boolean;
  lightDues: boolean;
  clientId: string | null;
  clientName: string;
  vesselId: string | null;
  vesselName: string;
  locationId: string;
  clusterID: string | null;
  clusterName: string;
  offshoreAssetsName: string;
  isFlexableTiming: boolean;
  arrivalTime: Date | null;
  firstInstallationTime: Date | null;
  latestArrivalTime: Date | null;
  clusterTime: number | null;
  timeUnit: string;
  isMailbag: boolean;
  isBulkReq: boolean;
  clientReference: string;
  remarks: string;
  comment: string;
  startEta: Date | null;
  endEtd: Date | null;
  status: SailingRequestStatus;
  outboundVoyageId: string | null;
  inboundVoyageId: string | null;
  inboundVoyage: any | null;
  outboundVoyage: Voyage | null;
  type: SailingRequestType;
  sailingRequestActivities: SailingRequestActivity[];
  sailingRequestAssets: SailingRequestAsset[];
  sailingRequestUserComments: SailingRequestUserComment[] | null;
  transportRequests: TransportRequest[];
  deleted: boolean;
  weeklyPattern: string;
  repeatEveryNumberOfWeeks: number;
  isInbound: boolean;
  isOutbound: boolean;
  isInterfield: boolean;
  inboundLifts: number;
  inboundLiftPercentage: number;
  inboundAtQuayCargoesCount: number;
  inboundAllCargoesCount: number;
  inboundAtQuayCargoesPercentage: number;
  inboundTotalLiftsCount: number;
  inboundVoyageStatus?: VoyageStatus;

  outboundLifts: number;
  outboundLiftPercentage: number;
  outboundAtQuayCargoesCount: number;
  outboundAllCargoesCount: number;
  outboundAtQuayCargoesPercentage: number;
  outboundTotalLiftsCount: number;
  outboundVoyageStatus?: VoyageStatus;
}
