import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
  inject,
  SimpleChanges,
  OnChanges,
  ChangeDetectorRef,
  HostListener,
  OnDestroy,
  DestroyRef,
} from '@angular/core';
import { Mat<PERSON>idenav, MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatListModule } from '@angular/material/list';
import { AsyncPipe, NgFor, NgIf, NgClass, CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  FormControl,
  ReactiveFormsModule,
  Validators,
  FormsModule,
  FormGroup,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { Observable, Subject, Subscription, startWith } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import {
  NgxMatSelectSearchModule,
  MatSelectSearchComponent,
} from 'ngx-mat-select-search';
import { DatePipe } from '@angular/common';
import { NgxMatDatetimePickerModule } from '@angular-material-components/datetime-picker';
import { MatDialog } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { lookaheadFeature } from '../../../../../../libs/services/src/lib/services/lookahead/store/features';
import { assetsFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';
import { RequestSailingRequirementComponent } from './requirement/request-sailing-requirement.component';
import { LookaheadSharedService } from '../../../../../../libs/services/src/lib/services/shared/lookahead-subject.service';
import { ActivityCategory } from '../../../../../../libs/services/src/lib/services/maintenance/interfaces/activity-category.interface';
import { RequestSailingCommentsComponent } from './request-sailing-comments/request-sailing-comments.component';
import { ConfirmComponent } from 'libs/components/src/lib/components/confirm/confirm.component';
import { Confirm } from 'libs/components/src/lib/interfaces/confirm.interface';
import { SailingRequest } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request.interface';
import { SailingRequestAsset } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request-asset.interface';
import { Unit } from '../../../../../../libs/services/src/lib/services/interfaces/unit.interface';
import { LookaheadActions } from '../../../../../../libs/services/src/lib/services/lookahead/store/actions/lookahead.action';
import { RequestSailingApprovalComponent } from './approval/request-sailing-approval.component';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { SailingRequestActivity } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request-activity.interface';
import * as moment from 'moment';
import { User } from 'libs/auth/src/lib/interfaces/user.interface';
import { SailingRequestUserComment } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request-user-comment.interface';
import { ClientLocation } from 'libs/services/src/lib/services/client-locations/interfaces/client-locations.interface';
import { confirmActions } from 'libs/components/src/lib/store/confirm.actions';
import { UtilityService } from '../../../../../../libs/services/src/lib/services/utility.service';
import { planningDetailsFeature } from '../../../../../../libs/services/src/lib/services/voyages/store/features/planning-details.feature';
import { PlanningDetailsActions } from '../../../../../../libs/services/src/lib/services/voyages/store/actions/planning-details.action';
import { Actions, ofType } from '@ngrx/effects';
import { PlanningDetails } from '../../../../../../libs/services/src/lib/services/voyages/interfaces/planning-details.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PlanningDetailsService } from '../../shared/services/planning-details.service';
import { stripTimezoneOffset } from '../../../../../../libs/services/src/lib/services/functions/convert-date.utils';
import { TimezoneService } from '../../../../../../libs/services/src/lib/services/timezone.service';

@Component({
  selector: 'request-sailing',
  templateUrl: './request-sailing.component.html',
  styleUrls: ['./request-sailing.component.scss'],
  standalone: true,
  imports: [
    MatSidenavModule,
    MatIconModule,
    MatButtonModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    NgIf,
    NgFor,
    MatTabsModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatTooltipModule,
    ReactiveFormsModule,
    NgxMatSelectSearchModule,
    AsyncPipe,
    FormsModule,
    NgxMatDatetimePickerModule,
    MatProgressSpinnerModule,
    RequestSailingRequirementComponent,
    RequestSailingCommentsComponent,
    RequestSailingApprovalComponent,
    CommonModule,
  ],
  providers: [MatSelectSearchComponent, DatePipe],
})
export class RequestSailingComponent implements OnInit, OnChanges, OnDestroy {
  @ViewChild(RequestSailingRequirementComponent)
  requirementComponent!: RequestSailingRequirementComponent;
  @ViewChild(RequestSailingApprovalComponent)
  approvalComponent!: RequestSailingApprovalComponent;
  private planningDetailsService = inject(PlanningDetailsService);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  datePipe = inject(DatePipe);
  cdr = inject(ChangeDetectorRef);
  dialog = inject(MatDialog);
  store = inject(Store);
  sharedService = inject(LookaheadSharedService);
  utilityService = inject(UtilityService);
  timezoneService = inject(TimezoneService);
  vm$ = this.store.select(lookaheadFeature.selectLookaheadsState);
  sailingRequestUserComments$ = this.store.select(
    lookaheadFeature.selectSailingRequestComments
  );
  unsubscribe: Subject<boolean> = new Subject();
  @ViewChild(RequestSailingCommentsComponent)
  RequestSailingCommentsComponent!: RequestSailingCommentsComponent;
  @Input() isSidenavOpened = false;
  @Output() isSidenavOpenedChange = new EventEmitter<boolean>();
  @Output() isInboundOutboundSidenavOpenedChange = new EventEmitter<{
    isOpen: boolean;
    voyageType: string;
    vesselId: string;
  }>();
  @Output() resetFormChange = new EventEmitter();
  @Input() originalAssets: Asset[] = [];
  @Input() vessels: Vessel[] = [];
  @Input() activityCategories: ActivityCategory[] | undefined;
  @Input() units: Unit[] | undefined;
  @Input() currentUser: User | undefined;
  @Input() clients: ClientLocation[] | undefined;
  assets: Asset[] = [];
  requestSailingId: string = '';
  sailingRequestUserComments: SailingRequestUserComment[] = [];
  headerText = 'Single occurrence';
  searchClusterControl = new FormControl('');
  searchInstallationControl = new FormControl('');
  private subscriptions = new Subscription();
  editMode = false;
  originalClusterAssets: Asset[] = [];
  clusterAssets: Asset[] = [];
  offshoreInstallations = this.store.selectSignal(
    assetsFeature.selectOffshoreAssets
  );
  inboundPlanningDetails = this.store.selectSignal(
    planningDetailsFeature.selectInboundPlanningDetails
  );
  outboundPlanningDetails = this.store.selectSignal(
    planningDetailsFeature.selectOutboundPlanningDetails
  );

  saveData: any;
  @ViewChild('sidenav') sidenav!: MatSidenav;
  selectedTabIndex: number = 0;
  showErrors: boolean = false;
  showApprovalErrors: boolean = false;

  form = new FormGroup(
    {
      clusterId: new FormControl<string | null>(null),
      sailingRequestAssets: new FormControl<string[]>(
        [],
        [Validators.required]
      ),
      isInbound: new FormControl<boolean>(false, [Validators.required]),
      isOutbound: new FormControl<boolean>(false, [Validators.required]),
      isInterfield: new FormControl<boolean>(false, [Validators.required]),
      vesselId: new FormControl<string | null>(null),
      clientId: new FormControl<string | null>(null),
      inboundVoyageId: new FormControl<string | null>(null),
      outboundVoyageId: new FormControl<string | null>(null),
      startTime: new FormControl<Date | null>(null),
      endTime: new FormControl<Date | null>(null),
      eta: new FormControl<string>(''),
      etd: new FormControl<string>(''),
      status: new FormControl<number>(0),
      comment: new FormControl<string>(''),
      isFlexableTiming: new FormControl<boolean>(false),
      arrivalTime: new FormControl<Date | null>(null),
      firstInstallationTime: new FormControl<Date | null>(null),
      latestArrivalTime: new FormControl<Date | null>(null),
      clusterTime: new FormControl<number | null>(null),
      timeUnit: new FormControl<string>(''),
      isMailbag: new FormControl<boolean>(false),
      isBulkReq: new FormControl<boolean>(false),
      clientReference: new FormControl<string>(''),
      remarks: new FormControl<string>(''),
      sailingRequestActivities: new FormControl<SailingRequestActivity[]>([]),
      seriesStartTime: new FormControl<Date | null>(null),
      seriesEndTime: new FormControl<Date | null>(null),
      weeklyPattern: new FormControl<string>(''),
      repeatEveryNumberOfWeeks: new FormControl<number | null>(null),
      selectAll: new FormControl<boolean>(false),
      autoFillCluster: new FormControl(true),
    },
    { validators: this.atLeastOneToggleValidator }
  );
  selectedDate: Date = new Date();
  interfieldHover: boolean = false;
  inboundHover: boolean = false;
  outboundHover: boolean = false;
  isFormDirty = false;
  approvalData: [] = [];
  addUserToComments = false;
  nextButtonClicked = false;
  viewTransportRequests = false;
  isPartOfCluster = true;
  originalOffshoreInstallations: Asset[] = [];
  filteredOffshoreInstallations: Asset[] = [];
  filteredActivities: ActivityCategory[] = [];

  ngOnInit() {
    this.filteredOffshoreInstallations = this.offshoreInstallations();

    this.subscriptions.add(
      this.sharedService.editPoolSailingRequestSubject.subscribe((data) => {
        this.editMode = true;
        this.store.dispatch(
          LookaheadActions.load_Sailing_Request_User_Comments({
            sailingRequestId: data.Id,
          })
        );
        this.subToSailingRequestUserComments();
        this.requestSailingId = data.Id;
        this.filters();
        this.patchForm(data);
      })
    );
    this.subscriptions.add(
      this.sharedService.resetRequestSailingFormSubject.subscribe(() => {
        this.resetRequestSailingForm();
      })
    );

    this.form.patchValue({ startTime: new Date() });

    this.filters();
    this.subscribeToControlChanges('isInbound');
    this.subscribeToControlChanges('isOutbound');
    this.subscribeToControlChanges('isInterfield');
    this.subscribeToControlChanges('isInterfield');
  }

  ngOnChanges(changes: SimpleChanges) {
    this.originalClusterAssets = this.utilityService.getClusterAssets(
      this.originalAssets
    );
    if (
      changes['originalAssets'] &&
      !changes['originalAssets'].isFirstChange()
    ) {
      this.assets = Object.assign([], this.originalAssets);
      this.filters();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private patchForm(sailingRequest: any): void {
    this.isPartOfCluster = true;

    const sailingRequestAssets = sailingRequest.SailingRequestAssets ?? [];

    // Check and adjust ETA/ETD for DST if needed
    let adjustedEta = sailingRequest.Eta;
    let adjustedEtd = sailingRequest.Etd;

    if (
      sailingRequest.StartTime &&
      sailingRequest.EndTime &&
      sailingRequest.Eta &&
      sailingRequest.Etd
    ) {
      const startTime = new Date(sailingRequest.StartTime);
      const endTime = new Date(sailingRequest.EndTime);

      // Check if times need DST adjustment
      const dstAdjustment = this.timezoneService.checkAndAdjustTimesForDST(
        startTime,
        endTime,
        sailingRequest.Eta,
        sailingRequest.Etd,
        sailingRequest.OriginalTimezone // This would need to be stored when creating the request
      );

      if (dstAdjustment.dstAdjustmentMade) {
        adjustedEta = dstAdjustment.adjustedEta;
        adjustedEtd = dstAdjustment.adjustedEtd;

        // Log the adjustment for debugging
        console.log('DST adjustment made:', {
          originalEta: sailingRequest.Eta,
          adjustedEta,
          originalEtd: sailingRequest.Etd,
          adjustedEtd,
        });
      }
    }

    this.form.patchValue({
      clusterId: sailingRequest.ClusterId,
      isInbound: sailingRequest.IsInbound,
      isOutbound: sailingRequest.IsOutbound,
      isInterfield: sailingRequest.IsInterfield,
      clientId: sailingRequest.ClientId,
      vesselId: sailingRequest.VesselId,
      sailingRequestAssets: sailingRequestAssets.map(
        (asset: any) => asset.assetId
      ),
      startTime: sailingRequest.StartTime,
      endTime: sailingRequest.EndTime,
      eta: adjustedEta,
      etd: adjustedEtd,
      inboundVoyageId: sailingRequest.InboundVoyageId,
      outboundVoyageId: sailingRequest.OutboundVoyageId,
      weeklyPattern: sailingRequest.WeeklyPattern,
      repeatEveryNumberOfWeeks: sailingRequest.RepeatEveryNumberOfWeeks,
      sailingRequestActivities: sailingRequest.SailingRequestActivities,
      status: sailingRequest.Status,
      comment: sailingRequest.Comment,
      isFlexableTiming: sailingRequest.IsFlexableTiming,
      arrivalTime: stripTimezoneOffset(new Date(sailingRequest.ArrivalTime)),
      firstInstallationTime: sailingRequest.FirstInstallationTime
        ? stripTimezoneOffset(new Date(sailingRequest.FirstInstallationTime))
        : null,
      latestArrivalTime: sailingRequest.LatestArrivalTime
        ? stripTimezoneOffset(new Date(sailingRequest.LatestArrivalTime))
        : null,
      clusterTime: sailingRequest.ClusterTime,
      timeUnit: sailingRequest.TimeUnit,
      isMailbag: sailingRequest.IsMailbag,
      isBulkReq: sailingRequest.IsBulkReq,
      clientReference: sailingRequest.ClientReference,
      remarks: sailingRequest.Remarks,
    });
    if (
      sailingRequest.SeriesStartTime !== null &&
      sailingRequest.SeriesEndTime !== null
    ) {
      this.headerText = 'Series';
      this.form.controls.seriesStartTime.setValue(
        sailingRequest.SeriesStartTime
      );
      this.form.controls.seriesEndTime.setValue(sailingRequest.SeriesEndTime);
    }

    if (sailingRequest.ClusterId) {
      this.onClusterChange(sailingRequest.ClusterId, true);
    } else if (sailingRequestAssets.length > 0) {
      this.checkIfAssetBelongsToCluster();
    }

    this.inboundHover = sailingRequest.IsInbound;
    this.outboundHover = sailingRequest.IsOutbound;
    this.interfieldHover = sailingRequest.IsInterfield;
  }

  private subToSailingRequestUserComments(): void {
    this.sailingRequestUserComments$
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((res) => {
        this.sailingRequestUserComments = res;
        this.showNotificationOnComments();
      });
  }

  private subscribeToControlChanges(controlName: string) {
    const control = this.form.get(controlName);
    if (control) {
      control.valueChanges.subscribe(() => {
        control.markAsTouched(); // Manually mark the control as touched
        this.checkFormDirty();
      });
      control.statusChanges.subscribe(() => {
        this.checkFormDirty();
      });
    }
  }

  @HostListener('window:beforeunload', ['$event'])
  unloadHandler(event: Event) {
    if (this.form.dirty) {
      const result = confirm('Changes you made may not be saved.');
      if (!result) {
        event.returnValue = false;
      }
    }
  }

  handleDataFromChild(event: {
    formData: any;
    sailingRequestActivities: any[];
  }) {
    const { formData, sailingRequestActivities } = event;
    this.patchRequirementFormData(formData, sailingRequestActivities);
  }

  approvalDataChanged(approvalData: SailingRequest) {
    this.patchApprovalFormData(approvalData);
  }

  patchRequirementFormData(formData: any, sailingRequestActivities: any[]) {
    this.form.patchValue({
      clientId: formData.clientId,
      isFlexableTiming: formData.isFlexableTiming,
      arrivalTime: this.convertMomentToDate(formData.arrivalTime),
      firstInstallationTime: formData.firstInstallationTime,
      latestArrivalTime: formData.latestArrivalTime,
      clusterTime: formData.clusterTime,
      timeUnit: formData.timeUnit,
      isMailbag: formData.isMailbag,
      isBulkReq: formData.isBulkReq,
      clientReference: formData.clientReference,
      remarks: formData.remarks,
      endTime: formData.endTime,
      weeklyPattern: formData.weeklyPattern,
      seriesStartTime: formData.seriesStartTime,
      seriesEndTime: formData.seriesEndTime,
      repeatEveryNumberOfWeeks: formData.repeatEveryNumberOfWeeks,
      sailingRequestActivities: sailingRequestActivities,
    });
  }

  patchApprovalFormData(approvalData: any) {
    this.form.patchValue({
      vesselId: approvalData.vesselId,
      inboundVoyageId: approvalData.inboundVoyageId,
      outboundVoyageId: approvalData.outboundVoyageId,
      comment: approvalData.comment,
      startTime: approvalData.startTime,
      endTime: approvalData.endTime,
      weeklyPattern: approvalData.weeklyPattern,
      seriesStartTime: approvalData.seriesStartTime,
      seriesEndTime: approvalData.seriesEndTime,
      repeatEveryNumberOfWeeks: approvalData.repeatEveryNumberOfWeeks,
      eta: approvalData.eta,
      etd: approvalData.etd,
      status: approvalData.status,
    });
  }

  showNotificationOnComments() {
    if (this.sailingRequestUserComments.length) {
      const lastComment =
        this.sailingRequestUserComments?.[
          this.sailingRequestUserComments.length - 1
        ];
      const isLastCommentByCurrentUser =
        lastComment.createdByUserId === this.currentUser?.userId;
      if (isLastCommentByCurrentUser) {
        return;
      }
      const hasUserReadAllComments = lastComment?.commentReadByUsers?.some(
        (x: any) => x.readerUserId === this.currentUser?.userId
      );
      if (hasUserReadAllComments) {
        return;
      }
      this.addUserToComments = true;
    }
  }
  onTabChange(event: any) {
    if (event.index === 3) {
      this.selectedTabIndex = 4;
      if (this.editMode) {
        this.RequestSailingCommentsComponent.EnableAutoScroll();
        this.RequestSailingCommentsComponent.ScrollToBottom();
        if (this.addUserToComments) {
          this.RequestSailingCommentsComponent.AddUserToCommentsReadBy();
        }
        this.addUserToComments = false;
      }
    }
  }

  toggleRequest() {
    if (
      this.form.dirty ||
      (this.editMode &&
        this.RequestSailingCommentsComponent.IsCommentsFormDirty())
    ) {
      this.openConfirmationDialog();
    } else {
      this.sharedService.resetRequestSailingForm();
      this.sharedService.closeInboundOutboundSidenav();
      if (this.editMode) {
        this.RequestSailingCommentsComponent.ResetForm();
      }
    }
  }

  allowChildToSendComment() {
    this.editMode && this.RequestSailingCommentsComponent.AddComment();
  }

  onClusterChange(selectedClusterId: string, isFromPatch = false) {
    this.searchInstallationControl.setValue('');

    if (!isFromPatch) {
      this.form.get('sailingRequestAssets')?.setValue([]);
    }

    if (!this.originalOffshoreInstallations.length) {
      this.originalOffshoreInstallations = this.offshoreInstallations();
    }

    if (selectedClusterId) {
      // filter by head, member, or explicit children
      const cluster = this.originalClusterAssets.find(
        (c) => c.assetId === selectedClusterId
      );
      this.filteredOffshoreInstallations =
        this.originalOffshoreInstallations.filter(
          (asset) =>
            asset.assetId === selectedClusterId ||
            asset.clusterHeadId === selectedClusterId ||
            cluster?.clusterChildren?.includes(asset.assetId)
        );
    } else {
      // restore full list when no cluster selected
      this.filteredOffshoreInstallations = this.originalOffshoreInstallations;
    }

    this.cdr.detectChanges();
  }

  private filters(): void {
    this.searchClusterControl.valueChanges
      .pipe(
        startWith(''),
        map((value) => this._filterCluster(value!))
      )
      .subscribe((filteredAssets) => {
        this.clusterAssets = filteredAssets;
      });

    this.searchInstallationControl.valueChanges
      .pipe(
        startWith(''),
        map((searchValue) => this._filterInstallation(searchValue!))
      )
      .subscribe((filteredAssets) => {
        this.filteredOffshoreInstallations = filteredAssets;
      });

    if (
      !this.filteredOffshoreInstallations?.length &&
      this.offshoreInstallations()?.length
    ) {
      this.originalOffshoreInstallations = this.offshoreInstallations();
      this.filteredOffshoreInstallations = this.originalOffshoreInstallations;
    }

    this.cdr.detectChanges();
  }

  private _filterCluster(value: string | null): Asset[] {
    if (value === '') {
      return this.originalClusterAssets ?? [];
    }

    const filterValue = value!.toLowerCase();
    const numericFilterValue = value!.toString().toLowerCase();

    return this.originalClusterAssets!.filter(
      (asset) =>
        asset.name.toLowerCase().includes(filterValue) ||
        asset.name.toLowerCase().includes(numericFilterValue)
    );
  }

  private _filterInstallation(value: string | null): Asset[] {
    if (!this.originalOffshoreInstallations || !this.offshoreInstallations()) {
      return [];
    }

    if (value === '') {
      return this.originalOffshoreInstallations ?? [];
    }

    const filterValue = value!.toLowerCase();
    const numericFilterValue = value!.toString().toLowerCase();

    return this.originalOffshoreInstallations.filter(
      (asset) =>
        asset.name.toLowerCase().includes(filterValue) ||
        asset.name.toLowerCase().includes(numericFilterValue)
    );
  }

  clearClusterSelection() {
    this.form.get('clusterId')?.setValue(null);
    this.searchClusterControl.setValue('');
    this.form.get('sailingRequestAssets')?.setValue([]);
    this.searchInstallationControl.setValue('');

    this.filteredOffshoreInstallations =
      this.originalOffshoreInstallations || this.offshoreInstallations();

    this.cdr.detectChanges();
  }

  // There is a copy of this in the Request App's request-sailing.component.ts/html
  // This is to prevent the two apps possible future requirements from conflicting
  checkIfAssetBelongsToCluster() {
    // get selected installations
    const selected = this.form.get('sailingRequestAssets')?.value || [];
    if (!selected.length) {
      this.isPartOfCluster = true;
      this.filteredOffshoreInstallations = this.originalOffshoreInstallations
        ?.length
        ? this.originalOffshoreInstallations
        : this.offshoreInstallations();
      return;
    }

    const installations = this.offshoreInstallations();
    // preserve original list
    if (!this.originalOffshoreInstallations.length) {
      this.originalOffshoreInstallations = installations;
    }

    const installation = installations.find(
      (inst) => inst.assetId === selected[0]
    );
    if (!installation) return;

    // determine cluster head ID if any
    let headId: string | null = null;
    const isClusterHead = this.originalClusterAssets.some(
      (c) => c.assetId === installation.assetId
    );
    const isClusterMember = Boolean(
      installation.clusterHeadId &&
        this.originalClusterAssets.some(
          (c) => c.assetId === installation.clusterHeadId
        )
    );

    if (isClusterHead) headId = installation.assetId;
    else if (isClusterMember) headId = installation.clusterHeadId!;

    if (headId) {
      // update form and filter by cluster membership
      this.isPartOfCluster = true;
      this.form.get('clusterId')?.setValue(headId);
      const cluster = this.originalClusterAssets.find(
        (c) => c.assetId === headId
      )!;
      this.filteredOffshoreInstallations =
        this.originalOffshoreInstallations.filter(
          (asset) =>
            asset.assetId === headId ||
            asset.clusterHeadId === headId ||
            cluster.clusterChildren?.includes(asset.assetId)
        );
      this.onClusterChange(headId, true);
    } else {
      // no cluster: disable dropdown and show only standalone assets
      this.isPartOfCluster = false;
      this.form.get('clusterId')?.setValue(null);
      this.filteredOffshoreInstallations =
        this.originalOffshoreInstallations.filter(
          (asset) =>
            !asset.clusterHeadId &&
            !this.originalClusterAssets.some((c) => c.assetId === asset.assetId)
        );
    }

    this.cdr.detectChanges();
  }

  onAutoFillClusterChange(event: any) {
    // If we have installations selected, try to determine the cluster
    const selectedAssets = this.form.get('sailingRequestAssets')?.value;
    if (selectedAssets && selectedAssets.length > 0) {
      this.autoFillClusterFromInstallations();
    }
  }

  autoFillClusterFromInstallations() {
    const selectedInstallations = this.form.get('sailingRequestAssets')?.value;
    if (!selectedInstallations || selectedInstallations.length === 0) return;

    const firstInstallationId = selectedInstallations[0];
    const installation = this.offshoreInstallations().find(
      (inst: Asset) => inst.assetId === firstInstallationId
    );

    if (installation && installation.clusterHeadId) {
      // Set the cluster ID to the installation's cluster head ID
      this.form.get('clusterId')?.setValue(installation.clusterHeadId);
      if (installation.clusterHeadId) {
        this.onClusterChange(installation.clusterHeadId, true);
      } else if (selectedInstallations.length > 0) {
        this.checkIfAssetBelongsToCluster();
      }
    }
  }

  clearInstallationSelection() {
    this.form.get('sailingRequestAssets')?.setValue([]);
    this.searchInstallationControl.setValue('');
    this.isPartOfCluster = true;
  }

  onInstallationSelectionChange() {
    const selectedInstallations = this.form.get('sailingRequestAssets')?.value;

    // If no installations are selected, clear the cluster dropdown
    if (!selectedInstallations || selectedInstallations.length === 0) {
      this.form.get('clusterId')?.setValue(null);
      this.isPartOfCluster = true;
      this.filteredOffshoreInstallations =
        this.originalOffshoreInstallations || this.offshoreInstallations();
      return;
    }

    this.checkIfAssetBelongsToCluster();
  }

  convertMomentToDate(date: any): Date | null {
    if (moment.isMoment(date)) {
      return date.toDate();
    }
    return date;
  }

  openConfirmationDialog() {
    const dialogRef = this.dialog.open(ConfirmComponent, {
      width: '600px',
      data: {
        titles: {
          title: 'Unsaved Changes',
          body: 'You have unsaved changes. Are you sure you want to proceed without saving?',
          btnConfirm: 'Proceed',
          btnReject: 'Cancel',
        },
        confirm: { type: '' },
        reject: { type: '' },
      } as Confirm,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.sharedService.resetRequestSailingForm();
        this.sharedService.closeInboundOutboundSidenav();
        if (this.editMode) {
          this.RequestSailingCommentsComponent.ResetForm();
        }
      }
    });
  }

  resetRequestSailingForm() {
    this.isSidenavOpened = !this.isSidenavOpened;
    this.isSidenavOpenedChange.emit(this.isSidenavOpened);
    this.resetForm();
    this.selectedTabIndex = 0;
  }

  outboundNotSelected(): boolean {
    return (
      this.form.get('isInterfield')?.value! &&
      !this.form.get('isOutbound')?.value
    );
  }

  voyageTypeNotSelected(): boolean {
    return (
      !this.form.get('isInbound')?.value &&
      !this.form.get('isOutbound')?.value &&
      !this.form.get('isInterfield')?.value
    );
  }

  isRequirementDisabled(): boolean {
    const isVoyageDirectionNotSelected =
      !this.form.get('isInbound')?.value &&
      !this.form.get('isOutbound')?.value &&
      !this.form.get('isInterfield')?.value;

    const installationsNotSelected = !this.form.get('sailingRequestAssets')
      ?.value?.length;

    if (isVoyageDirectionNotSelected || installationsNotSelected) return true;
    else return false;
  }

  isApprovalsDisabled(): boolean {
    if (this.requirementComponent?.form?.invalid) {
      return true;
    } else return false;
  }

  goToNextTab() {
    this.form.markAsTouched();
    this.showErrors = true;
    this.nextButtonClicked = true;
    switch (this.selectedTabIndex) {
      case 0: {
        if (
          !this.voyageTypeNotSelected() &&
          this.form.get('sailingRequestAssets')?.value?.length
        ) {
          this.selectedTabIndex += 1;
          this.showErrors = false;
        }
        break;
      }
      case 1: {
        if (!this.requirementComponent.form.invalid) {
          this.selectedTabIndex += 1;
          this.showErrors = false;
        }
        break;
      }
      case 2: {
        if (!this.approvalComponent.form.invalid) {
          this.selectedTabIndex += 1;
          this.showErrors = false;
        }
        break;
      }
    }
  }

  hasOnlyCliRoleInPlan(): boolean {
    const planRole = this.currentUser?.roles.find(
      (role: any) => role.application === 'plan'
    );
    return planRole
      ? planRole.roles.length === 1 && planRole.roles.includes('CLI')
      : false;
  }

  save() {
    this.showApprovalErrors = true;

    const etaValidationFailed =
      !this.hasOnlyCliRoleInPlan() &&
      this.approvalComponent.isEtaRequired() &&
      (!this.approvalComponent.form.get('eta')?.value ||
        this.approvalComponent.form.get('eta')?.value === '');

    const etdValidationFailed =
      !this.hasOnlyCliRoleInPlan() &&
      this.approvalComponent.isEtdRequired() &&
      (!this.approvalComponent.form.get('etd')?.value ||
        this.approvalComponent.form.get('etd')?.value === '');

    if (
      this.form.invalid ||
      this.requirementComponent.form.invalid ||
      (!this.hasOnlyCliRoleInPlan() && this.approvalComponent.form.invalid) ||
      etaValidationFailed ||
      etdValidationFailed
    ) {
      this.requirementComponent.form.markAllAsTouched();
      this.approvalComponent.form.markAllAsTouched();
      return;
    }

    const formValue = this.form.value;

    let weeklyPattern = formValue.weeklyPattern;
    if (Array.isArray(weeklyPattern)) {
      weeklyPattern = weeklyPattern.join(',');
    }

    const sailingRequestAssets: SailingRequestAsset[] =
      this.form.value.sailingRequestAssets!.map((assetId) => {
        return {
          assetId: assetId,
          name: '',
        };
      });

    let startTime = formValue.startTime
      ? new Date(
          formValue.startTime.getTime() -
            formValue.startTime.getTimezoneOffset() * 60000
        )
          .toISOString()
          .slice(0, 10)
      : null;
    let endTime = formValue.endTime
      ? new Date(
          formValue.endTime.getTime() -
            formValue.endTime.getTimezoneOffset() * 60000
        )
          .toISOString()
          .slice(0, 10)
      : null;

    let seriesStartTime;
    let seriesEndTime;

    if (
      this.form.value.seriesStartTime !== null &&
      this.form.value.seriesEndTime !== null
    ) {
      seriesStartTime = formValue.seriesStartTime
        ? new Date(
            formValue.seriesStartTime.getTime() -
              formValue.seriesStartTime.getTimezoneOffset() * 60000
          )
            .toISOString()
            .slice(0, 10)
        : null;
      seriesEndTime = formValue.seriesEndTime
        ? new Date(
            formValue.seriesEndTime.getTime() -
              formValue.seriesEndTime.getTimezoneOffset() * 60000
          )
            .toISOString()
            .slice(0, 10)
        : null;
    } else {
      seriesStartTime = null;
      seriesEndTime = null;
    }

    const formattedEta = this.formatTime(formValue.eta);
    const formattedEtd = this.formatTime(formValue.etd);

    const clientId = this.hasOnlyCliRoleInPlan()
      ? this.currentUser?.clientId
      : this.form.value.clientId;

    const activities =
      this.form.value.sailingRequestActivities?.map((activity) => ({
        ...activity,
        sailingRequestActivityId: '00000000-0000-0000-0000-000000000000',
        sailingRequestId: '00000000-0000-0000-0000-000000000000',
        activityCategoryTypeActivityCategoryActivityType: 0,
      })) ?? [];

    const arrivalTime = this.form.get('arrivalTime')?.value;
    const firstInstallationTime = this.form.get('firstInstallationTime')?.value;
    const latestArrivalTime = this.form.get('latestArrivalTime')?.value;

    const model = {
      ...this.form.value,
      arrivalTime: arrivalTime
        ? stripTimezoneOffset(new Date(arrivalTime))
        : null,
      firstInstallationTime: firstInstallationTime
        ? stripTimezoneOffset(new Date(firstInstallationTime))
        : null,
      latestArrivalTime: latestArrivalTime
        ? stripTimezoneOffset(new Date(latestArrivalTime))
        : null,
      clientId: clientId,
      sailingRequestAssets,
      eta: formattedEta,
      etd: formattedEtd,
      type: 0,
      startTime: startTime,
      endTime: endTime,
      seriesStartTime: seriesStartTime,
      seriesEndTime: seriesEndTime,
      weeklyPattern: weeklyPattern,
      sailingRequestActivities: activities,
    } as Partial<SailingRequest> as SailingRequest;

    model.firstInstallationTime = model.isFlexableTiming
      ? null
      : model.firstInstallationTime;
    model.latestArrivalTime = model.isFlexableTiming
      ? model.latestArrivalTime
      : null;

    this.syncVoyagePlanning(model);

    if (this.editMode) {
      this.store.dispatch(
        LookaheadActions.edit_Sailing_Request({
          sailingRequestId: this.requestSailingId,
          sailingRequest: model,
        })
      );
    } else {
      this.store.dispatch(
        LookaheadActions.add_Sailing_Request({ sailingRequest: model })
      );
    }
  }

  syncVoyagePlanning(model: SailingRequest) {
    const outboundDetails = this.outboundPlanningDetails();
    const inboundDetails = this.inboundPlanningDetails();
    const startDate = this.planningDetailsService.combineDateAndTimeString(
      model.startTime!,
      model.eta!
    );
    const endDate = this.planningDetailsService.combineDateAndTimeString(
      model.endTime!,
      model.etd!
    );
    this.planningDetailsService.handleOutboundDetails(
      outboundDetails,
      endDate,
      model.outboundVoyageId
    );
    this.planningDetailsService.handleInboundDetails(
      inboundDetails,
      startDate,
      model.inboundVoyageId
    );
  }

  deleteSailingRequest(): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title: 'Are you sure you would like to delete this Sailing Request?',
          btnConfirm: 'Yes Delete',
        },
        confirm: LookaheadActions.remove_Sailing_Request({
          id: this.requestSailingId,
        }),
      })
    );
  }

  resetForm() {
    const resetValues = {
      clusterId: '',
      sailingRequestAssets: [],
      isInbound: false,
      isOutbound: false,
      isInterfield: false,
      vesselId: null,
      clientId: null,
      inboundVoyageId: null,
      outboundVoyageId: null,
      startTime: null,
      endTime: null,
      eta: '',
      etd: '',
      status: 0,
      comment: '',
      isFlexableTiming: false,
      arrivalTime: null,
      firstInstallationTime: null,
      latestArrivalTime: null,
      clusterTime: null,
      timeUnit: '',
      isMailbag: false,
      isBulkReq: false,
      clientReference: '',
      remarks: '',
      sailingRequestActivities: [],
      seriesStartTime: null,
      seriesEndTime: null,
      weeklyPattern: '',
      repeatEveryNumberOfWeeks: null,
      selectAll: false,
      autoFillCluster: false,
    };

    this.form.reset(resetValues);
    this.form.markAsPristine();
    this.form.markAsUntouched();
    this.resetFormChange.emit();
    this.inboundHover = false;
    this.outboundHover = false;
    this.interfieldHover = false;

    // Reset filtered installations to original list
    this.isPartOfCluster = true;
    this.filteredOffshoreInstallations = this.originalOffshoreInstallations
      ?.length
      ? this.originalOffshoreInstallations
      : this.offshoreInstallations();
    this.searchInstallationControl.setValue('');

    setTimeout(() => {
      this.editMode = false;
    }, 500);
  }

  inboundSelect() {
    this.form.controls.isInbound.setValue(!this.form.controls.isInbound.value);
  }

  outboundSelect() {
    this.form.controls.isOutbound.setValue(
      !this.form.controls.isOutbound.value
    );
  }

  interfieldSelect() {
    this.form.controls.isInterfield.setValue(
      !this.form.controls.isInterfield.value
    );
  }

  selectAll(event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.form.controls.isInbound.setValue(isChecked);
    this.form.controls.isOutbound.setValue(isChecked);
    this.form.controls.isInterfield.setValue(isChecked);
  }

  atLeastOneToggleValidator(control: AbstractControl): ValidationErrors | null {
    const inboundToggle = control.get('isInbound')?.value;
    const outboundToggle = control.get('isOutbound')?.value;
    const interfieldToggle = control.get('isInterfield')?.value;

    if (interfieldToggle && !outboundToggle) {
      return { noToggleSelected: false };
    }

    return inboundToggle || outboundToggle ? null : { noToggleSelected: true };
  }

  private checkFormDirty() {
    const inboundControl = this.form.get('isInbound');
    const outboundControl = this.form.get('isOutbound');
    const interfieldControl = this.form.get('isInterfield');

    const inboundTouched = inboundControl?.touched ?? false;
    const outboundTouched = outboundControl?.touched ?? false;
    const interfieldTouched = interfieldControl?.touched ?? false;

    const inboundValue = inboundControl?.value ?? false;
    const outboundValue = outboundControl?.value ?? false;
    const interfieldValue = interfieldControl?.value ?? false;

    const anyToggleSelected = inboundValue || outboundValue || interfieldValue;
    const anyToggleTouched =
      inboundTouched || outboundTouched || interfieldTouched;

    if (inboundValue && !outboundValue && !interfieldValue) {
      this.filteredActivities = this.filterActivitiesForInbound();
    } else if (!inboundValue && (outboundValue || interfieldValue)) {
      this.filteredActivities = this.activityCategories!;
    } else {
      this.filteredActivities = this.activityCategories!;
    }

    this.isFormDirty = anyToggleTouched && !anyToggleSelected;
  }

  private filterActivitiesForInbound(): any[] {
    return this.activityCategories!.map((category) => ({
      ...category,
      activityCategoryTypes: category.activityCategoryTypes.filter(
        (type) => !type.createOutOfPortActivity
      ),
    })).filter((category) => category.activityCategoryTypes.length > 0);
  }

  formatTime(time: string | null | undefined): string | null {
    if (!time) {
      return null;
    }
    const timeParts = time.split(':');
    if (timeParts.length >= 2) {
      const hours = timeParts[0].padStart(2, '0');
      const minutes = timeParts[1].padStart(2, '0');
      return `${hours}:${minutes}:00`;
    }
    return null;
  }

  /**
   * Method to manually check and adjust ETA/ETD times for DST
   * This can be called when user suspects timezone issues
   */
  checkAndAdjustForDST(): void {
    const startTime = this.form.get('startTime')?.value;
    const endTime = this.form.get('endTime')?.value;
    const eta = this.form.get('eta')?.value;
    const etd = this.form.get('etd')?.value;

    if (startTime && endTime && eta && etd) {
      const dstAdjustment = this.timezoneService.checkAndAdjustTimesForDST(
        new Date(startTime),
        new Date(endTime),
        eta,
        etd
      );

      if (dstAdjustment.dstAdjustmentMade) {
        this.form.patchValue({
          eta: dstAdjustment.adjustedEta,
          etd: dstAdjustment.adjustedEtd,
        });

        // Show user notification about the adjustment
        console.log('Manual DST adjustment applied:', {
          originalEta: eta,
          adjustedEta: dstAdjustment.adjustedEta,
          originalEtd: etd,
          adjustedEtd: dstAdjustment.adjustedEtd,
        });
      } else {
        console.log('No DST adjustment needed');
      }
    }
  }

  /**
   * Gets timezone information for debugging purposes
   */
  getTimezoneDebugInfo(): void {
    const startTime = this.form.get('startTime')?.value;
    if (startTime) {
      const info = this.timezoneService.getTimezoneInfo(new Date(startTime));
      console.log('Timezone Debug Info:', info);
    }
  }
}
