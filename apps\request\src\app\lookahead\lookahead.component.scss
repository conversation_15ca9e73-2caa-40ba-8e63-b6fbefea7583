@import url('https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap');

.request-button {
  font-family: 'Ubuntu', sans-serif;
  float: right;
  width: auto;
  height: 38px;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  border-radius: 4px;
  padding: 0 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  mat-icon {
    margin-right: 8px;
    font-size: 20px;
  }

  span {
    margin-bottom: 0;
  }
}

.placeholder-box {
  width: 100%;
  height: 100px;
  background-color: rgba(233, 233, 233, 0.6);
  border: 1px solid #d2cbcb;
  text-align: center;
  line-height: 100px;
}

.sidenav {
  width: 400px;
  padding: 16px;
  background-color: #f7f7f7;
  transition: 0.3s all ease;
}

.sidenav-voyages {
  width: 400px;
  margin-right: 400px;
  background-color: #f7f7f7;
  transition: 0.3s all ease;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-container h1 {
  margin: 0;
}

.request-button {
  margin-left: auto;
}

.scheduler {
  height: 100%;
}

.row {
  display: flex;
}

.column {
  flex: 1;
}

:host ::ng-deep .mat-sidenav-container .mat-drawer-backdrop {
  background-color: transparent;
}

.additional-content {
  padding: 20px;
}

.add-icon {
  margin-top: 10px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin-top: 100px;
}

.lookahead-title {
  margin-left: 15px !important;
}

.client-search {
  margin-top: 20px;
}

:host
  ::ng-deep
  .mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled)
  .mat-mdc-text-field-wrapper {
  background-color: white !important;
  height: 55px;
}

.tabs-identifiers-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.sailing-request-tabs {
  max-width: 600px;
  flex: 1;
}

.identifiers-container {
  display: flex;
  gap: 16px;
  margin-left: auto;
}

.identifier {
  display: flex;
  align-items: center;
  gap: 8px;
}

.circle,
.triangle,
.arrow {
  width: 16px;
  height: 16px;
}

.circle {
  border-radius: 50%;
}

.purple {
  background-color: #dfd8fd;
  border: 2px solid #5e4db2;
}

.gold {
  background-color: #ffebb4;
  border: 2px solid #80620d;
}

.blue {
  background-color: #c2ebfc;
  border: 2px solid #457791;
}

:host ::ng-deep * {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

:host ::ng-deep *::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Opera */
}
